<template>
  <header class="app-header">
    <div class="header-container">
      <!-- Logo区域 -->
      <div class="logo-section">
        <router-link to="/" class="logo-link">
          <img src="/logo.png" alt="晓闻科技" class="logo-img" />
          <span class="logo-text">晓闻科技</span>
        </router-link>
      </div>

      <!-- 导航菜单 -->
      <nav class="nav-menu">
        <router-link to="/" class="nav-item" active-class="active"
          >首页</router-link
        >
        <a href="https://open.xiaowen.tech" class="nav-item" target="_blank"
          >开放平台</a
        >
        <a href="https://pet.xiaowen.tech" class="nav-item" target="_blank"
          >小闻养宠助手</a
        >
        <a href="https://vet.xiaowen.tech" class="nav-item" target="_blank"
          >好兽医AI助手</a
        >
        <router-link to="/cooperation" class="nav-item">合作计划</router-link>
        <router-link to="/about" class="nav-item">关于我们</router-link>
      </nav>

      <!-- 移动端菜单按钮 -->
      <div class="mobile-menu-btn" @click="toggleMobileMenu">
        <span class="menu-line"></span>
        <span class="menu-line"></span>
        <span class="menu-line"></span>
      </div>
    </div>

    <!-- 移动端菜单 -->
    <div class="mobile-menu" :class="{ active: mobileMenuOpen }">
      <router-link to="/" class="mobile-nav-item" @click="closeMobileMenu"
        >首页</router-link
      >
      <a
        href="https://open.xiaowen.tech"
        class="mobile-nav-item"
        target="_blank"
        @click="closeMobileMenu"
        >开放平台</a
      >
      <a
        href="https://pet.xiaowen.tech"
        class="mobile-nav-item"
        target="_blank"
        @click="closeMobileMenu"
        >小闻养宠助手</a
      >
      <a
        href="https://vet.xiaowen.tech"
        class="mobile-nav-item"
        target="_blank"
        @click="closeMobileMenu"
        >好兽医AI助手</a
      >
      <router-link
        to="/cooperation"
        class="mobile-nav-item"
        @click="closeMobileMenu"
        >合作计划</router-link
      >
      <router-link to="/about" class="mobile-nav-item" @click="closeMobileMenu"
        >关于我们</router-link
      >
    </div>
  </header>
</template>

<script lang="ts" setup>
import { ref } from "vue";

const mobileMenuOpen = ref(false);

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value;
};

const closeMobileMenu = () => {
  mobileMenuOpen.value = false;
};
</script>

<style scoped>
.app-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 87, 255, 0.1);
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Logo区域 */
.logo-section {
  display: flex;
  align-items: center;
}

.logo-link {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #333;
  transition: all 0.3s ease;
}

.logo-link:hover {
  transform: translateY(-1px);
}

.logo-img {
  height: 40px;
  width: auto;
  margin-right: 12px;
}

.logo-text {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #0057ff, #00a8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 导航菜单 */
.nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-item {
  position: relative;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 0;
  transition: all 0.3s ease;
}

.nav-item:hover,
.nav-item.active {
  color: #0057ff;
}

.nav-item::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #0057ff, #00a8ff);
  transition: width 0.3s ease;
}

.nav-item:hover::after,
.nav-item.active::after {
  width: 100%;
}

/* 移动端菜单按钮 */
.mobile-menu-btn {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
}

.menu-line {
  width: 24px;
  height: 3px;
  background: #333;
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* 移动端菜单 */
.mobile-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 87, 255, 0.1);
  padding: 20px 24px;
  transform: translateY(-10px);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu.active {
  transform: translateY(0);
  opacity: 1;
  visibility: visible;
}

.mobile-nav-item {
  display: block;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  font-size: 16px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 87, 255, 0.1);
  transition: all 0.3s ease;
}

.mobile-nav-item:last-child {
  border-bottom: none;
}

.mobile-nav-item:hover {
  color: #0057ff;
  padding-left: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
    height: 60px;
  }

  .logo-img {
    height: 32px;
  }

  .logo-text {
    font-size: 20px;
  }

  .nav-menu {
    display: none;
  }

  .mobile-menu-btn {
    display: flex;
  }

  .mobile-menu {
    display: block;
  }
}

@media (max-width: 480px) {
  .header-container {
    padding: 0 12px;
  }

  .logo-text {
    font-size: 18px;
  }
}
</style>
