<template>
  <div class="home-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="hero-container">
        <div class="hero-content">
          <h1 class="hero-title">
            <span class="title-main">晓闻科技</span>
            <span class="title-sub">宠物医疗健康管理领域的AI科技创新企业</span>
          </h1>
          <p class="hero-description">
            通过AI技术赋能宠物医疗健康全产业链，为宠物主人、兽医师和行业合作伙伴提供智能化解决方案
          </p>
          <div class="hero-buttons">
            <button class="btn-primary" @click="scrollToProducts">
              探索产品
            </button>
            <button class="btn-secondary" @click="scrollToAbout">
              了解更多
            </button>
          </div>
        </div>
        <div class="hero-visual">
          <div class="hero-image">
            <div class="floating-card card-1">
              <div class="card-icon">🐕</div>
              <div class="card-text">AI诊疗助手</div>
            </div>
            <div class="floating-card card-2">
              <div class="card-icon">🏥</div>
              <div class="card-text">智能健康管理</div>
            </div>
            <div class="floating-card card-3">
              <div class="card-icon">📱</div>
              <div class="card-text">移动端应用</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Company Info Section -->
    <section class="company-info-section" id="about">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">关于晓闻科技</h2>
          <p class="section-subtitle">专注宠物医疗健康管理的AI科技创新</p>
        </div>
        <div class="company-stats">
          <div class="stat-item">
            <div class="stat-number">2025</div>
            <div class="stat-label">成立年份</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">深圳</div>
            <div class="stat-label">总部位置</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">AI+</div>
            <div class="stat-label">核心技术</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">全产业链</div>
            <div class="stat-label">服务范围</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Section -->
    <section class="products-section" id="products">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">产品矩阵</h2>
          <p class="section-subtitle">覆盖宠物医疗健康全产业链的AI解决方案</p>
        </div>
        <div class="products-grid">
          <!-- B2B产品线 -->
          <div class="product-category">
            <h3 class="category-title">B2B产品线</h3>
            <p class="category-subtitle">面向合作伙伴</p>
            <div class="product-cards">
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">🔗</div>
                  <h4 class="card-title">晓闻AI开放平台</h4>
                </div>
                <p class="card-description">
                  提供API接口和技术服务，助力合作伙伴快速集成AI能力
                </p>
                <a
                  href="https://open.xiaowen.tech"
                  class="card-link"
                  target="_blank"
                >
                  访问平台 →
                </a>
              </div>
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">🏢</div>
                  <h4 class="card-title">宠物健康管理解决方案</h4>
                </div>
                <p class="card-description">
                  企业级SaaS产品，为宠物医院提供全方位数字化管理
                </p>
                <a href="#" class="card-link"> 了解详情 → </a>
              </div>
            </div>
          </div>

          <!-- 专业用户产品线 -->
          <div class="product-category">
            <h3 class="category-title">专业用户产品线</h3>
            <p class="category-subtitle">面向兽医师</p>
            <div class="product-cards">
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">💻</div>
                  <h4 class="card-title">好兽医AI助手Web端</h4>
                </div>
                <p class="card-description">
                  专业诊疗辅助工具，提供智能诊断建议和治疗方案
                </p>
                <a
                  href="https://vet.xiaowen.tech"
                  class="card-link"
                  target="_blank"
                >
                  立即使用 →
                </a>
              </div>
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">📱</div>
                  <h4 class="card-title">好兽医AI助手App</h4>
                </div>
                <p class="card-description">
                  移动端专业应用，随时随地获得AI诊疗支持
                </p>
                <a href="#" class="card-link"> 下载应用 → </a>
              </div>
            </div>
          </div>

          <!-- C端产品线 -->
          <div class="product-category">
            <h3 class="category-title">C端产品线</h3>
            <p class="category-subtitle">面向宠物主人</p>
            <div class="product-cards">
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">🌐</div>
                  <h4 class="card-title">小闻养宠助手Web端</h4>
                </div>
                <p class="card-description">
                  宠物健康管理平台，记录宠物成长，获得专业建议
                </p>
                <a
                  href="https://pet.xiaowen.tech"
                  class="card-link"
                  target="_blank"
                >
                  立即体验 →
                </a>
              </div>
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">📲</div>
                  <h4 class="card-title">小闻养宠助手App/小程序</h4>
                </div>
                <p class="card-description">
                  移动端养宠工具，随时关注宠物健康状态
                </p>
                <a href="#" class="card-link"> 下载应用 → </a>
              </div>
            </div>
          </div>

          <!-- 行业服务产品线 -->
          <div class="product-category">
            <h3 class="category-title">行业服务产品线</h3>
            <p class="category-subtitle">面向AI模型开发</p>
            <div class="product-cards">
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">📊</div>
                  <h4 class="card-title">宠物医疗评测数据管理平台</h4>
                </div>
                <p class="card-description">
                  专业数据服务平台，为AI模型训练提供高质量数据集
                </p>
                <a
                  href="https://data.xiaowen.tech"
                  class="card-link"
                  target="_blank"
                >
                  访问平台 →
                </a>
              </div>
              <div class="product-card">
                <div class="card-header">
                  <div class="card-icon">🧪</div>
                  <h4 class="card-title">宠物医疗大模型评测平台</h4>
                </div>
                <p class="card-description">
                  模型测试评估工具，确保AI模型的准确性和可靠性
                </p>
                <a
                  href="https://data.xiaowen.tech"
                  class="card-link"
                  target="_blank"
                >
                  了解详情 →
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Architecture Section -->
    <section class="architecture-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">网站架构</h2>
          <p class="section-subtitle">完整的产品生态系统</p>
        </div>
        <div class="architecture-grid">
          <div class="arch-item main-site">
            <div class="arch-icon">🏠</div>
            <h3 class="arch-title">企业门户</h3>
            <p class="arch-domain">xiaowen.tech</p>
            <p class="arch-description">公司官网，展示企业信息和产品矩阵</p>
          </div>
          <div class="arch-item">
            <div class="arch-icon">🔧</div>
            <h3 class="arch-title">B2B平台</h3>
            <p class="arch-domain">open.xiaowen.tech</p>
            <p class="arch-description">API文档和开发者中心</p>
          </div>
          <div class="arch-item">
            <div class="arch-icon">👨‍⚕️</div>
            <h3 class="arch-title">专业用户平台</h3>
            <p class="arch-domain">vet.xiaowen.tech</p>
            <p class="arch-description">好兽医AI助手Web版</p>
          </div>
          <div class="arch-item">
            <div class="arch-icon">🐾</div>
            <h3 class="arch-title">C端平台</h3>
            <p class="arch-domain">pet.xiaowen.tech</p>
            <p class="arch-description">小闻养宠助手Web版</p>
          </div>
          <div class="arch-item">
            <div class="arch-icon">📈</div>
            <h3 class="arch-title">行业服务平台</h3>
            <p class="arch-domain">data.xiaowen.tech</p>
            <p class="arch-description">数据服务和模型评测</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer-section">
      <div class="container">
        <div class="footer-content">
          <div class="footer-info">
            <div class="footer-logo">
              <img src="/logo.png" alt="晓闻科技" class="footer-logo-img" />
              <span class="footer-logo-text">晓闻科技</span>
            </div>
            <p class="footer-description">
              宠物医疗健康管理领域的AI科技创新企业
            </p>
            <p class="footer-copyright">© 2025 晓闻科技. 保留所有权利.</p>
          </div>
          <div class="footer-links">
            <div class="link-group">
              <h4 class="link-title">产品</h4>
              <a href="https://open.xiaowen.tech" target="_blank">开放平台</a>
              <a href="https://vet.xiaowen.tech" target="_blank"
                >好兽医AI助手</a
              >
              <a href="https://pet.xiaowen.tech" target="_blank"
                >小闻养宠助手</a
              >
              <a href="https://data.xiaowen.tech" target="_blank"
                >数据服务平台</a
              >
            </div>
            <div class="link-group">
              <h4 class="link-title">公司</h4>
              <a href="#about">关于我们</a>
              <a href="#products">产品矩阵</a>
              <a href="/cooperation">合作计划</a>
              <a href="/contact">联系我们</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  </div>
</template>

<script lang="ts" setup>
const scrollToProducts = () => {
  const element = document.getElementById("products");
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};

const scrollToAbout = () => {
  const element = document.getElementById("about");
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
};
</script>

<style scoped>
/* 全局样式 */
.home-page {
  padding-top: 70px; /* 为固定头部留出空间 */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-title {
  font-size: 48px;
  font-weight: 700;
  color: #333;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #0057ff, #00a8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 20px;
  color: #666;
  line-height: 1.6;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #f8faff 0%, #e8f4ff 100%);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e0e7ff" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  position: relative;
  z-index: 1;
}

.hero-content {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  margin-bottom: 24px;
}

.title-main {
  display: block;
  font-size: 64px;
  font-weight: 800;
  background: linear-gradient(135deg, #0057ff, #00a8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
}

.title-sub {
  display: block;
  font-size: 24px;
  font-weight: 400;
  color: #666;
  line-height: 1.4;
}

.hero-description {
  font-size: 18px;
  color: #666;
  line-height: 1.8;
  margin-bottom: 40px;
}

.hero-buttons {
  display: flex;
  gap: 20px;
}

.btn-primary,
.btn-secondary {
  padding: 16px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: linear-gradient(135deg, #0057ff, #00a8ff);
  color: white;
  box-shadow: 0 8px 24px rgba(0, 87, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 87, 255, 0.4);
}

.btn-secondary {
  background: white;
  color: #0057ff;
  border: 2px solid #0057ff;
}

.btn-secondary:hover {
  background: #0057ff;
  color: white;
  transform: translateY(-2px);
}

/* Hero Visual */
.hero-visual {
  position: relative;
  height: 500px;
  animation: fadeInRight 1s ease-out 0.3s both;
}

.hero-image {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  overflow: hidden;
}

.floating-card {
  position: absolute;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  animation: float 3s ease-in-out infinite;
}

.card-1 {
  top: 20%;
  left: -10%;
  animation-delay: 0s;
}

.card-2 {
  top: 50%;
  right: -10%;
  animation-delay: 1s;
}

.card-3 {
  bottom: 20%;
  left: 20%;
  animation-delay: 2s;
}

.card-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.card-text {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Company Info Section */
.company-info-section {
  padding: 100px 0;
  background: white;
}

.company-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.stat-item {
  text-align: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f8faff, #e8f4ff);
  border-radius: 20px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 87, 255, 0.15);
}

.stat-number {
  font-size: 36px;
  font-weight: 800;
  color: #0057ff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

/* Products Section */
.products-section {
  padding: 100px 0;
  background: linear-gradient(135deg, #f8faff 0%, #e8f4ff 100%);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 60px;
}

.product-category {
  background: white;
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.product-category:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
}

.category-title {
  font-size: 28px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.category-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
}

.product-cards {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.product-card {
  background: linear-gradient(135deg, #f8faff, #e8f4ff);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateX(8px);
  background: linear-gradient(135deg, #e8f4ff, #d8ecff);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  font-size: 24px;
  margin-right: 12px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.card-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 16px;
}

.card-link {
  color: #0057ff;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
}

.card-link:hover {
  color: #003db3;
}

/* Architecture Section */
.architecture-section {
  padding: 100px 0;
  background: white;
}

.architecture-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.arch-item {
  text-align: center;
  padding: 40px 24px;
  background: linear-gradient(135deg, #f8faff, #e8f4ff);
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.arch-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0057ff, #00a8ff);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.arch-item:hover::before {
  transform: scaleX(1);
}

.arch-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 87, 255, 0.15);
}

.main-site {
  grid-column: 1 / -1;
  background: linear-gradient(135deg, #0057ff, #00a8ff);
  color: white;
}

.main-site .arch-title,
.main-site .arch-domain,
.main-site .arch-description {
  color: white;
}

.arch-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.arch-title {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 8px;
}

.arch-domain {
  font-size: 16px;
  font-weight: 600;
  color: #0057ff;
  margin-bottom: 12px;
  font-family: "Monaco", "Menlo", monospace;
}

.main-site .arch-domain {
  color: rgba(255, 255, 255, 0.9);
}

.arch-description {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* Footer Section */
.footer-section {
  background: #1a1a1a;
  color: white;
  padding: 60px 0 40px;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.footer-info {
  max-width: 400px;
}

.footer-logo {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.footer-logo-img {
  height: 40px;
  width: auto;
  margin-right: 12px;
}

.footer-logo-text {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #0057ff, #00a8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-description {
  font-size: 16px;
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 24px;
}

.footer-copyright {
  font-size: 14px;
  color: #999;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
}

.link-group {
  display: flex;
  flex-direction: column;
}

.link-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.link-group a {
  color: #ccc;
  text-decoration: none;
  font-size: 14px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.link-group a:hover {
  color: #0057ff;
  padding-left: 8px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }

  .hero-visual {
    height: 400px;
  }

  .products-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .architecture-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-site {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .home-page {
    padding-top: 60px;
  }

  .container {
    padding: 0 16px;
  }

  .section-title {
    font-size: 36px;
  }

  .section-subtitle {
    font-size: 18px;
  }

  .title-main {
    font-size: 48px;
  }

  .title-sub {
    font-size: 20px;
  }

  .hero-description {
    font-size: 16px;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    max-width: 280px;
  }

  .company-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
  }

  .stat-number {
    font-size: 28px;
  }

  .architecture-grid {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-links {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 28px;
  }

  .title-main {
    font-size: 36px;
  }

  .title-sub {
    font-size: 18px;
  }

  .hero-visual {
    height: 300px;
  }

  .company-stats {
    grid-template-columns: 1fr;
  }

  .product-category {
    padding: 24px;
  }

  .category-title {
    font-size: 24px;
  }

  .floating-card {
    padding: 16px;
  }

  .card-icon {
    font-size: 24px;
  }

  .card-text {
    font-size: 12px;
  }
}
</style>
